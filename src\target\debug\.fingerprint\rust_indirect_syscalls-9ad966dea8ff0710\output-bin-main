{"$message_type":"diagnostic","message":"function `log_memory_protection` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":10083,"byte_end":10104,"line_start":204,"line_end":204,"column_start":11,"column_end":32,"is_primary":true,"text":[{"text":"unsafe fn log_memory_protection(logger: &mut Logger, address: *mut c_void, _size: usize, description: &str) {","highlight_start":11,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `log_memory_protection` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:204:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0munsafe fn log_memory_protection(logger: &mut Logger, address: *mut c_void, _size: usize, description: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `log_shellcode_memory_details` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":13987,"byte_end":14015,"line_start":261,"line_end":261,"column_start":11,"column_end":39,"is_primary":true,"text":[{"text":"unsafe fn log_shellcode_memory_details() {","highlight_start":11,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `log_shellcode_memory_details` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:261:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0munsafe fn log_shellcode_memory_details() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `MEMORY_BASIC_INFORMATION` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":1489,"byte_end":1513,"line_start":36,"line_end":36,"column_start":16,"column_end":40,"is_primary":true,"text":[{"text":"    pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32 }","highlight_start":16,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `MEMORY_BASIC_INFORMATION` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:36:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `with_logger` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":5941,"byte_end":5952,"line_start":133,"line_end":133,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn with_logger<F: FnOnce(&mut logger::Logger)>(_f: F) {}","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `with_logger` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:133:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_logger<F: FnOnce(&mut logger::Logger)>(_f: F) {}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":6352,"byte_end":6363,"line_start":137,"line_end":137,"column_start":157,"column_end":168,"is_primary":false,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":157,"highlight_end":168}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":6995,"byte_end":6999,"line_start":137,"line_end":137,"column_start":800,"column_end":804,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":800,"highlight_end":804}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7074,"byte_end":7085,"line_start":137,"line_end":137,"column_start":879,"column_end":890,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":879,"highlight_end":890}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7185,"byte_end":7202,"line_start":137,"line_end":137,"column_start":990,"column_end":1007,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":990,"highlight_end":1007}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7320,"byte_end":7335,"line_start":137,"line_end":137,"column_start":1125,"column_end":1140,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":1125,"highlight_end":1140}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7433,"byte_end":7446,"line_start":137,"line_end":137,"column_start":1238,"column_end":1251,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":1238,"highlight_end":1251}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7548,"byte_end":7563,"line_start":137,"line_end":137,"column_start":1353,"column_end":1368,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":1353,"highlight_end":1368}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7601,"byte_end":7619,"line_start":137,"line_end":137,"column_start":1406,"column_end":1424,"is_primary":true,"text":[{"text":"mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }","highlight_start":1406,"highlight_end":1424}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:137:800\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, \"{}\", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!(\"[INFO] {}\", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!(\"[DEBUG] {}\", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!(\"[ERROR] {}\", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!(\"[TRACE] {}\", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!(\"[WARN] {}\", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!(\"{}: {:p}\", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!(\"Calling {}: {:?}\", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!(\"Starting operation: {}\", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!(\"Finished operation: {}\", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&m\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\u001b[0m                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m                                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"5 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 5 warnings emitted\u001b[0m\n\n"}
