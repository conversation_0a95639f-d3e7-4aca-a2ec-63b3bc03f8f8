{"$message_type":"diagnostic","message":"structure field `Machine` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2173,"byte_end":2180,"line_start":84,"line_end":84,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    pub Machine: u16,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2173,"byte_end":2180,"line_start":84,"line_end":84,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    pub Machine: u16,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"machine","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Machine` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:84:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Machine: u16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `machine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfSections` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2195,"byte_end":2211,"line_start":85,"line_end":85,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub NumberOfSections: u16,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2195,"byte_end":2211,"line_start":85,"line_end":85,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub NumberOfSections: u16,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"number_of_sections","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfSections` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:85:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfSections: u16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_sections`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `TimeDateStamp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2226,"byte_end":2239,"line_start":86,"line_end":86,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub TimeDateStamp: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2226,"byte_end":2239,"line_start":86,"line_end":86,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub TimeDateStamp: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"time_date_stamp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `TimeDateStamp` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:86:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub TimeDateStamp: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `time_date_stamp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToSymbolTable` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2254,"byte_end":2274,"line_start":87,"line_end":87,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToSymbolTable: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2254,"byte_end":2274,"line_start":87,"line_end":87,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToSymbolTable: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":"pointer_to_symbol_table","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToSymbolTable` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:87:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub PointerToSymbolTable: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_symbol_table`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfSymbols` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2289,"byte_end":2304,"line_start":88,"line_end":88,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub NumberOfSymbols: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2289,"byte_end":2304,"line_start":88,"line_end":88,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub NumberOfSymbols: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"number_of_symbols","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfSymbols` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:88:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfSymbols: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_symbols`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfOptionalHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2319,"byte_end":2339,"line_start":89,"line_end":89,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub SizeOfOptionalHeader: u16,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2319,"byte_end":2339,"line_start":89,"line_end":89,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub SizeOfOptionalHeader: u16,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":"size_of_optional_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfOptionalHeader` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:89:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfOptionalHeader: u16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_optional_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2354,"byte_end":2369,"line_start":90,"line_end":90,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u16,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2354,"byte_end":2369,"line_start":90,"line_end":90,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u16,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:90:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m90\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Characteristics: u16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2432,"byte_end":2446,"line_start":95,"line_end":95,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub VirtualAddress: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2432,"byte_end":2446,"line_start":95,"line_end":95,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub VirtualAddress: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":"virtual_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualAddress` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:95:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub VirtualAddress: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Size` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2461,"byte_end":2465,"line_start":96,"line_end":96,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Size: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2461,"byte_end":2465,"line_start":96,"line_end":96,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Size: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Size` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:96:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Size: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Magic` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2531,"byte_end":2536,"line_start":101,"line_end":101,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    pub Magic: u16,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2531,"byte_end":2536,"line_start":101,"line_end":101,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    pub Magic: u16,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"magic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Magic` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:101:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Magic: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `magic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorLinkerVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2551,"byte_end":2569,"line_start":102,"line_end":102,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub MajorLinkerVersion: u8,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2551,"byte_end":2569,"line_start":102,"line_end":102,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub MajorLinkerVersion: u8,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"major_linker_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorLinkerVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:102:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MajorLinkerVersion: u8,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_linker_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorLinkerVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2583,"byte_end":2601,"line_start":103,"line_end":103,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub MinorLinkerVersion: u8,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2583,"byte_end":2601,"line_start":103,"line_end":103,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub MinorLinkerVersion: u8,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"minor_linker_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorLinkerVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:103:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MinorLinkerVersion: u8,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_linker_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfCode` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2615,"byte_end":2625,"line_start":104,"line_end":104,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub SizeOfCode: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2615,"byte_end":2625,"line_start":104,"line_end":104,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub SizeOfCode: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"size_of_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfCode` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:104:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfCode: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfInitializedData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2640,"byte_end":2661,"line_start":105,"line_end":105,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub SizeOfInitializedData: u32,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2640,"byte_end":2661,"line_start":105,"line_end":105,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub SizeOfInitializedData: u32,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":"size_of_initialized_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfInitializedData` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:105:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfInitializedData: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_initialized_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfUninitializedData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2676,"byte_end":2699,"line_start":106,"line_end":106,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"    pub SizeOfUninitializedData: u32,","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2676,"byte_end":2699,"line_start":106,"line_end":106,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"    pub SizeOfUninitializedData: u32,","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":"size_of_uninitialized_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfUninitializedData` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:106:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfUninitializedData: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_uninitialized_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfEntryPoint` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2714,"byte_end":2733,"line_start":107,"line_end":107,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub AddressOfEntryPoint: u32,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2714,"byte_end":2733,"line_start":107,"line_end":107,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub AddressOfEntryPoint: u32,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"address_of_entry_point","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfEntryPoint` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:107:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub AddressOfEntryPoint: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_entry_point`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `BaseOfCode` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2748,"byte_end":2758,"line_start":108,"line_end":108,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub BaseOfCode: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2748,"byte_end":2758,"line_start":108,"line_end":108,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub BaseOfCode: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"base_of_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `BaseOfCode` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:108:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub BaseOfCode: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `base_of_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `ImageBase` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2773,"byte_end":2782,"line_start":109,"line_end":109,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub ImageBase: u64,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2773,"byte_end":2782,"line_start":109,"line_end":109,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub ImageBase: u64,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"image_base","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `ImageBase` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:109:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub ImageBase: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `image_base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SectionAlignment` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2797,"byte_end":2813,"line_start":110,"line_end":110,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub SectionAlignment: u32,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2797,"byte_end":2813,"line_start":110,"line_end":110,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub SectionAlignment: u32,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"section_alignment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SectionAlignment` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:110:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SectionAlignment: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `section_alignment`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FileAlignment` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2828,"byte_end":2841,"line_start":111,"line_end":111,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub FileAlignment: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2828,"byte_end":2841,"line_start":111,"line_end":111,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub FileAlignment: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"file_alignment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FileAlignment` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:111:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub FileAlignment: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `file_alignment`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorOperatingSystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2856,"byte_end":2883,"line_start":112,"line_end":112,"column_start":9,"column_end":36,"is_primary":true,"text":[{"text":"    pub MajorOperatingSystemVersion: u16,","highlight_start":9,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2856,"byte_end":2883,"line_start":112,"line_end":112,"column_start":9,"column_end":36,"is_primary":true,"text":[{"text":"    pub MajorOperatingSystemVersion: u16,","highlight_start":9,"highlight_end":36}],"label":null,"suggested_replacement":"major_operating_system_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorOperatingSystemVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:112:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m112\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MajorOperatingSystemVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_operating_system_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorOperatingSystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2898,"byte_end":2925,"line_start":113,"line_end":113,"column_start":9,"column_end":36,"is_primary":true,"text":[{"text":"    pub MinorOperatingSystemVersion: u16,","highlight_start":9,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2898,"byte_end":2925,"line_start":113,"line_end":113,"column_start":9,"column_end":36,"is_primary":true,"text":[{"text":"    pub MinorOperatingSystemVersion: u16,","highlight_start":9,"highlight_end":36}],"label":null,"suggested_replacement":"minor_operating_system_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorOperatingSystemVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:113:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MinorOperatingSystemVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_operating_system_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorImageVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2940,"byte_end":2957,"line_start":114,"line_end":114,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub MajorImageVersion: u16,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2940,"byte_end":2957,"line_start":114,"line_end":114,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub MajorImageVersion: u16,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"major_image_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorImageVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:114:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MajorImageVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_image_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorImageVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2972,"byte_end":2989,"line_start":115,"line_end":115,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub MinorImageVersion: u16,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2972,"byte_end":2989,"line_start":115,"line_end":115,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub MinorImageVersion: u16,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"minor_image_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorImageVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:115:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MinorImageVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_image_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorSubsystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3004,"byte_end":3025,"line_start":116,"line_end":116,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub MajorSubsystemVersion: u16,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3004,"byte_end":3025,"line_start":116,"line_end":116,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub MajorSubsystemVersion: u16,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":"major_subsystem_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorSubsystemVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:116:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MajorSubsystemVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_subsystem_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorSubsystemVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3040,"byte_end":3061,"line_start":117,"line_end":117,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub MinorSubsystemVersion: u16,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3040,"byte_end":3061,"line_start":117,"line_end":117,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub MinorSubsystemVersion: u16,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":"minor_subsystem_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorSubsystemVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:117:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MinorSubsystemVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_subsystem_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Win32VersionValue` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3076,"byte_end":3093,"line_start":118,"line_end":118,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub Win32VersionValue: u32,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3076,"byte_end":3093,"line_start":118,"line_end":118,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub Win32VersionValue: u32,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"win32_version_value","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Win32VersionValue` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:118:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Win32VersionValue: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `win32_version_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfImage` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3108,"byte_end":3119,"line_start":119,"line_end":119,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub SizeOfImage: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3108,"byte_end":3119,"line_start":119,"line_end":119,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub SizeOfImage: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"size_of_image","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfImage` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:119:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfImage: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_image`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeaders` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3134,"byte_end":3147,"line_start":120,"line_end":120,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub SizeOfHeaders: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3134,"byte_end":3147,"line_start":120,"line_end":120,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub SizeOfHeaders: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"size_of_headers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeaders` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:120:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfHeaders: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_headers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `CheckSum` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3162,"byte_end":3170,"line_start":121,"line_end":121,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    pub CheckSum: u32,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3162,"byte_end":3170,"line_start":121,"line_end":121,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    pub CheckSum: u32,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"check_sum","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `CheckSum` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:121:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub CheckSum: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `check_sum`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Subsystem` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3185,"byte_end":3194,"line_start":122,"line_end":122,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub Subsystem: u16,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3185,"byte_end":3194,"line_start":122,"line_end":122,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub Subsystem: u16,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"subsystem","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Subsystem` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:122:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Subsystem: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `subsystem`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `DllCharacteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3209,"byte_end":3227,"line_start":123,"line_end":123,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub DllCharacteristics: u16,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3209,"byte_end":3227,"line_start":123,"line_end":123,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub DllCharacteristics: u16,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"dll_characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `DllCharacteristics` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:123:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub DllCharacteristics: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `dll_characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfStackReserve` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3242,"byte_end":3260,"line_start":124,"line_end":124,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub SizeOfStackReserve: u64,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3242,"byte_end":3260,"line_start":124,"line_end":124,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub SizeOfStackReserve: u64,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"size_of_stack_reserve","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfStackReserve` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:124:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfStackReserve: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_stack_reserve`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfStackCommit` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3275,"byte_end":3292,"line_start":125,"line_end":125,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub SizeOfStackCommit: u64,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3275,"byte_end":3292,"line_start":125,"line_end":125,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub SizeOfStackCommit: u64,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"size_of_stack_commit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfStackCommit` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:125:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfStackCommit: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_stack_commit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeapReserve` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3307,"byte_end":3324,"line_start":126,"line_end":126,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub SizeOfHeapReserve: u64,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3307,"byte_end":3324,"line_start":126,"line_end":126,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub SizeOfHeapReserve: u64,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"size_of_heap_reserve","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeapReserve` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:126:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfHeapReserve: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_heap_reserve`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfHeapCommit` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3339,"byte_end":3355,"line_start":127,"line_end":127,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub SizeOfHeapCommit: u64,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3339,"byte_end":3355,"line_start":127,"line_end":127,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub SizeOfHeapCommit: u64,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"size_of_heap_commit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfHeapCommit` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:127:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfHeapCommit: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_heap_commit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `LoaderFlags` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3370,"byte_end":3381,"line_start":128,"line_end":128,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub LoaderFlags: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3370,"byte_end":3381,"line_start":128,"line_end":128,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub LoaderFlags: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"loader_flags","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `LoaderFlags` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:128:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub LoaderFlags: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `loader_flags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfRvaAndSizes` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3396,"byte_end":3415,"line_start":129,"line_end":129,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfRvaAndSizes: u32,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3396,"byte_end":3415,"line_start":129,"line_end":129,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfRvaAndSizes: u32,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"number_of_rva_and_sizes","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfRvaAndSizes` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:129:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfRvaAndSizes: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_rva_and_sizes`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `DataDirectory` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3430,"byte_end":3443,"line_start":130,"line_end":130,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16],","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3430,"byte_end":3443,"line_start":130,"line_end":130,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16],","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"data_directory","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `DataDirectory` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:130:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16],\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `data_directory`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Signature` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3527,"byte_end":3536,"line_start":135,"line_end":135,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub Signature: u32,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3527,"byte_end":3536,"line_start":135,"line_end":135,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub Signature: u32,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Signature` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:135:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Signature: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `signature`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `FileHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3551,"byte_end":3561,"line_start":136,"line_end":136,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub FileHeader: IMAGE_FILE_HEADER,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3551,"byte_end":3561,"line_start":136,"line_end":136,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub FileHeader: IMAGE_FILE_HEADER,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"file_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `FileHeader` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:136:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub FileHeader: IMAGE_FILE_HEADER,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `file_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `OptionalHeader` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3590,"byte_end":3604,"line_start":137,"line_end":137,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub OptionalHeader: IMAGE_OPTIONAL_HEADER64,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3590,"byte_end":3604,"line_start":137,"line_end":137,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub OptionalHeader: IMAGE_OPTIONAL_HEADER64,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":"optional_header","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `OptionalHeader` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:137:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub OptionalHeader: IMAGE_OPTIONAL_HEADER64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `optional_header`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3689,"byte_end":3704,"line_start":142,"line_end":142,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3689,"byte_end":3704,"line_start":142,"line_end":142,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:142:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Characteristics: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `TimeDateStamp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3719,"byte_end":3732,"line_start":143,"line_end":143,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub TimeDateStamp: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3719,"byte_end":3732,"line_start":143,"line_end":143,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub TimeDateStamp: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"time_date_stamp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `TimeDateStamp` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:143:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub TimeDateStamp: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `time_date_stamp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MajorVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3747,"byte_end":3759,"line_start":144,"line_end":144,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub MajorVersion: u16,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3747,"byte_end":3759,"line_start":144,"line_end":144,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub MajorVersion: u16,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"major_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MajorVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:144:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MajorVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `major_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `MinorVersion` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3774,"byte_end":3786,"line_start":145,"line_end":145,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub MinorVersion: u16,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3774,"byte_end":3786,"line_start":145,"line_end":145,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub MinorVersion: u16,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"minor_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `MinorVersion` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:145:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub MinorVersion: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `minor_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3801,"byte_end":3805,"line_start":146,"line_end":146,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Name: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3801,"byte_end":3805,"line_start":146,"line_end":146,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Name: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:146:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Name: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Base` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3820,"byte_end":3824,"line_start":147,"line_end":147,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Base: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3820,"byte_end":3824,"line_start":147,"line_end":147,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Base: u32,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"base","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Base` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:147:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Base: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfFunctions` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3839,"byte_end":3856,"line_start":148,"line_end":148,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub NumberOfFunctions: u32,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3839,"byte_end":3856,"line_start":148,"line_end":148,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub NumberOfFunctions: u32,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"number_of_functions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfFunctions` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:148:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfFunctions: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_functions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfNames` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3871,"byte_end":3884,"line_start":149,"line_end":149,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub NumberOfNames: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3871,"byte_end":3884,"line_start":149,"line_end":149,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub NumberOfNames: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"number_of_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfNames` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:149:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfNames: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfFunctions` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3899,"byte_end":3917,"line_start":150,"line_end":150,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub AddressOfFunctions: u32,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3899,"byte_end":3917,"line_start":150,"line_end":150,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub AddressOfFunctions: u32,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"address_of_functions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfFunctions` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:150:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub AddressOfFunctions: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_functions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfNames` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3932,"byte_end":3946,"line_start":151,"line_end":151,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub AddressOfNames: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3932,"byte_end":3946,"line_start":151,"line_end":151,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub AddressOfNames: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":"address_of_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfNames` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:151:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub AddressOfNames: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `AddressOfNameOrdinals` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3961,"byte_end":3982,"line_start":152,"line_end":152,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub AddressOfNameOrdinals: u32,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3961,"byte_end":3982,"line_start":152,"line_end":152,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    pub AddressOfNameOrdinals: u32,","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":"address_of_name_ordinals","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `AddressOfNameOrdinals` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:152:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub AddressOfNameOrdinals: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `address_of_name_ordinals`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4045,"byte_end":4049,"line_start":157,"line_end":157,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Name: [u8; 8],","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4045,"byte_end":4049,"line_start":157,"line_end":157,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Name: [u8; 8],","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:157:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Name: [u8; 8],\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Misc` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4068,"byte_end":4072,"line_start":158,"line_end":158,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Misc: IMAGE_SECTION_HEADER_MISC,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4068,"byte_end":4072,"line_start":158,"line_end":158,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub Misc: IMAGE_SECTION_HEADER_MISC,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"misc","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Misc` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:158:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Misc: IMAGE_SECTION_HEADER_MISC,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `misc`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4109,"byte_end":4123,"line_start":159,"line_end":159,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub VirtualAddress: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4109,"byte_end":4123,"line_start":159,"line_end":159,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    pub VirtualAddress: u32,","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":"virtual_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualAddress` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:159:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub VirtualAddress: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `SizeOfRawData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4138,"byte_end":4151,"line_start":160,"line_end":160,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub SizeOfRawData: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4138,"byte_end":4151,"line_start":160,"line_end":160,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub SizeOfRawData: u32,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"size_of_raw_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `SizeOfRawData` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:160:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub SizeOfRawData: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `size_of_raw_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToRawData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4166,"byte_end":4182,"line_start":161,"line_end":161,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub PointerToRawData: u32,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4166,"byte_end":4182,"line_start":161,"line_end":161,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    pub PointerToRawData: u32,","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"pointer_to_raw_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToRawData` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:161:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub PointerToRawData: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_raw_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToRelocations` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4197,"byte_end":4217,"line_start":162,"line_end":162,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToRelocations: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4197,"byte_end":4217,"line_start":162,"line_end":162,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToRelocations: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":"pointer_to_relocations","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToRelocations` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:162:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub PointerToRelocations: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_relocations`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PointerToLinenumbers` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4232,"byte_end":4252,"line_start":163,"line_end":163,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToLinenumbers: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4232,"byte_end":4252,"line_start":163,"line_end":163,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    pub PointerToLinenumbers: u32,","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":"pointer_to_linenumbers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PointerToLinenumbers` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:163:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub PointerToLinenumbers: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `pointer_to_linenumbers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfRelocations` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4267,"byte_end":4286,"line_start":164,"line_end":164,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfRelocations: u16,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4267,"byte_end":4286,"line_start":164,"line_end":164,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfRelocations: u16,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"number_of_relocations","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfRelocations` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:164:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m164\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfRelocations: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_relocations`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `NumberOfLinenumbers` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4301,"byte_end":4320,"line_start":165,"line_end":165,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfLinenumbers: u16,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4301,"byte_end":4320,"line_start":165,"line_end":165,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    pub NumberOfLinenumbers: u16,","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"number_of_linenumbers","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `NumberOfLinenumbers` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:165:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub NumberOfLinenumbers: u16,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `number_of_linenumbers`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `Characteristics` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4335,"byte_end":4350,"line_start":166,"line_end":166,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4335,"byte_end":4350,"line_start":166,"line_end":166,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub Characteristics: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"characteristics","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `Characteristics` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:166:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub Characteristics: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case (notice the capitalization): `characteristics`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `PhysicalAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4417,"byte_end":4432,"line_start":171,"line_end":171,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub PhysicalAddress: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4417,"byte_end":4432,"line_start":171,"line_end":171,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    pub PhysicalAddress: u32,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"physical_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `PhysicalAddress` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:171:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub PhysicalAddress: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `physical_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `VirtualSize` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4447,"byte_end":4458,"line_start":172,"line_end":172,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub VirtualSize: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4447,"byte_end":4458,"line_start":172,"line_end":172,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub VirtualSize: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"virtual_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `VirtualSize` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:172:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub VirtualSize: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `virtual_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `BeginAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4529,"byte_end":4541,"line_start":177,"line_end":177,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub BeginAddress: u32,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4529,"byte_end":4541,"line_start":177,"line_end":177,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub BeginAddress: u32,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"begin_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `BeginAddress` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:177:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub BeginAddress: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `begin_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `EndAddress` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4556,"byte_end":4566,"line_start":178,"line_end":178,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub EndAddress: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4556,"byte_end":4566,"line_start":178,"line_end":178,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub EndAddress: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"end_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `EndAddress` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:178:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub EndAddress: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `end_address`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `UnwindData` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4581,"byte_end":4591,"line_start":179,"line_end":179,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub UnwindData: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4581,"byte_end":4591,"line_start":179,"line_end":179,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub UnwindData: u32,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"unwind_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `UnwindData` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:179:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub UnwindData: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `unwind_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"68 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 68 warnings emitted\u001b[0m\n\n"}
