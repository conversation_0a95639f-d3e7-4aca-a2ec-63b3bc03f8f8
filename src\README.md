# Rust Indirect System Calls Implementation

This project is a 100% restoration of the indirect system call functionality from the original koneko project, implemented in Rust.

## Overview

The implementation includes:

1. **Indirect System Calls**: Using techniques like Hell's Gate, Halo's Gate, and Tartarus' Gate to find and execute system calls indirectly.
2. **Call Stack Spoofing**: Implementing the `Spoof` function to create synthetic call frames for stack spoofing.
3. **Fiber-based Shellcode Execution**: Using fibers to execute shellcode with infinite loop switching.
4. **Sleep Hooking**: Implementing hooks for Sleep and SleepEx functions to avoid detection.

## Key Components

- `syscalls.rs`: Implementation of the `CallMe` and `CallR12` functions for indirect system calls.
- `gadgets.rs`: Implementation of gadget collection and selection for ROP-based techniques.
- `spoof.rs`: Implementation of call stack spoofing functionality.
- `ssn_lookup.rs`: Implementation of syscall number lookup functionality.
- `fiber.rs`: Implementation of fiber-based shellcode execution.
- `sleep_hook.rs`: Implementation of sleep hooking functionality.

## Usage

### Building the Project

```bash
cargo build --release
```

### Running the Tests

```bash
cargo run --bin test_koneko_syscalls
```

### Running the Main Program

```bash
cargo run --bin koneko_rust
```

## Implementation Details

### Indirect System Calls

The implementation uses a combination of techniques to perform indirect system calls:

1. **CallMe Function**: Moves RCX to R10, loads the system service number into EAX, and jumps to the system call handler address.
2. **CallR12 Function**: Sets up registers and uses a gadget as a return address for stack spoofing.
3. **Syscall Number Lookup**: Finds the system service number (SSN) for a given syscall name.
4. **Gadget Collection**: Finds all instances of a given ROP gadget in a module.

### Call Stack Spoofing

The implementation uses the `Spoof` function to create synthetic call frames for stack spoofing, which helps evade stack-based detection mechanisms.

### Fiber-based Shellcode Execution

The implementation uses fibers to execute shellcode with infinite loop switching, which helps evade detection by hiding the execution flow.

### Sleep Hooking

The implementation hooks the Sleep and SleepEx functions to avoid detection by sandbox environments that measure sleep duration.

## Credits

This implementation is based on the original koneko project, which was created by Meowmycks.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
