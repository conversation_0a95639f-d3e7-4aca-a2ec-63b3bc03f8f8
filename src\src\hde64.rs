// Improved HDE64 disassembler based on mhde64.hpp
// This module provides a more robust implementation of the HDE64 disassembler
// for finding return addresses in functions

// Flags for instruction properties
#[allow(dead_code)]
pub enum Flags {
    FModrm = 0x00000001,
    FSib = 0x00000002,
    FImm8 = 0x00000004,
    FImm16 = 0x00000008,
    FImm32 = 0x00000010,
    FImm64 = 0x00000020,
    FDisp8 = 0x00000040,
    FDisp16 = 0x00000080,
    FDisp32 = 0x00000100,
    FRelative = 0x00000200,
    FError = 0x00001000,
    FErrorOpcode = 0x00002000,
    FErrorLength = 0x00004000,
    FErrorLock = 0x00008000,
    FErrorOperand = 0x00010000,
    FPrefixRepnz = 0x01000000,
    FPrefixRepx = 0x02000000,
    FPrefixRep = 0x03000000,
    FPrefix66 = 0x04000000,
    FPrefix67 = 0x08000000,
    FPrefixLock = 0x10000000,
    FPrefixSeg = 0x20000000,
    FPrefixRex = 0x40000000,
    FPrefixAny = 0x7f000000,
}

// Prefix bytes
#[allow(dead_code)]
pub enum Prefixes {
    PrefixSegmentCs = 0x2e,
    PrefixSegmentSs = 0x36,
    PrefixSegmentDs = 0x3e,
    PrefixSegmentEs = 0x26,
    PrefixSegmentFs = 0x64,
    PrefixSegmentGs = 0x65,
    PrefixLock = 0xf0,
    PrefixRepnz = 0xf2,
    PrefixRepx = 0xf3,
    PrefixOperandSize = 0x66,
    PrefixAddressSize = 0x67,
}

// HDE64 structure for disassembly
#[derive(Default, Debug)]
pub struct HDE64 {
    // Length of the instruction in bytes
    pub len: u8,
    
    // Prefixes
    pub p_rep: u8,    // Repeat prefix
    pub p_lock: u8,   // Lock prefix
    pub p_seg: u8,    // Segment override prefix
    pub p_66: u8,     // Operand size override prefix
    pub p_67: u8,     // Address size override prefix
    pub rex: u8,      // REX prefix
    pub rex_w: u8,    // REX.W bit
    pub rex_r: u8,    // REX.R bit
    pub rex_x: u8,    // REX.X bit
    pub rex_b: u8,    // REX.B bit
    
    // Opcodes
    pub opcode: u8,   // Primary opcode
    pub opcode2: u8,  // Secondary opcode
    
    // ModR/M byte
    pub modrm: u8,    // ModR/M byte
    pub modrm_mod: u8, // Mod field
    pub modrm_reg: u8, // Reg field
    pub modrm_rm: u8,  // R/M field
    
    // SIB byte
    pub sib: u8,       // SIB byte
    pub sib_scale: u8, // SIB scale factor
    pub sib_index: u8, // SIB index
    pub sib_base: u8,  // SIB base
    
    // Immediate data
    #[allow(dead_code)]
    pub imm: [u8; 8],  // Up to 64-bit immediate
    
    // Displacement data
    pub disp: [u8; 4], // Up to 32-bit displacement
    
    // Flags indicating various properties of the instruction
    pub flags: u32,
}

// Disassemble a single instruction
// Returns the length of the instruction, or 0 if disassembly failed
pub unsafe fn hde64_disasm(code: *const u8, hs: &mut HDE64, max_bytes: usize) -> u8 {
    if code.is_null() || max_bytes < 1 {
        return 0;
    }
    
    // Reset the structure
    *hs = HDE64::default();
    
    let mut p = code;
    let p_end = code.add(max_bytes);
    let mut c: u8;
    let mut i = 0;
    
    // Parse prefixes
    while i < 15 && p < p_end {
        c = *p;
        
        // Check for common prefixes
        if c == 0xF0 {
            hs.p_lock = 1;
            hs.flags |= Flags::FPrefixLock as u32;
        } else if c == 0xF2 {
            hs.p_rep = Flags::FPrefixRepnz as u8;
            hs.flags |= Flags::FPrefixRepnz as u32;
        } else if c == 0xF3 {
            hs.p_rep = Flags::FPrefixRep as u8;
            hs.flags |= Flags::FPrefixRep as u32;
        } else if c == 0x2E || c == 0x36 || c == 0x3E || c == 0x26 || c == 0x64 || c == 0x65 {
            hs.p_seg = c;
            hs.flags |= Flags::FPrefixSeg as u32;
        } else if c == 0x66 {
            hs.p_66 = 1;
            hs.flags |= Flags::FPrefix66 as u32;
        } else if c == 0x67 {
            hs.p_67 = 1;
            hs.flags |= Flags::FPrefix67 as u32;
        } else if c >= 0x40 && c <= 0x4F {
            // REX prefix
            hs.rex = c;
            hs.rex_w = (c >> 3) & 1;
            hs.rex_r = (c >> 2) & 1;
            hs.rex_x = (c >> 1) & 1;
            hs.rex_b = c & 1;
            hs.flags |= Flags::FPrefixRex as u32;
        } else {
            // Not a prefix, break
            break;
        }
        
        p = p.add(1);
        i += 1;
    }
    
    // Get the opcode
    if p >= p_end {
        return 0;
    }
    
    hs.opcode = *p;
    p = p.add(1);
    
    // Handle two-byte opcodes
    if hs.opcode == 0x0F {
        if p >= p_end {
            return 0;
        }
        
        hs.opcode2 = *p;
        p = p.add(1);
        
        // Set flags for two-byte opcodes
        hs.flags |= Flags::FModrm as u32;
    }
    
    // Check for ModR/M byte
    if (hs.opcode >= 0x80 && hs.opcode <= 0x8F) ||
       (hs.opcode >= 0xC0 && hs.opcode <= 0xCF && hs.opcode != 0xC3 && hs.opcode != 0xCB) ||
       (hs.opcode >= 0xD0 && hs.opcode <= 0xDF) ||
       (hs.opcode >= 0xF0) || // hs.opcode <= 0xFF is always true for u8
       (hs.flags & Flags::FModrm as u32 != 0) {
        
        if p >= p_end {
            return 0;
        }
        
        hs.modrm = *p;
        p = p.add(1);
        
        hs.modrm_mod = (hs.modrm >> 6) & 3;
        hs.modrm_reg = (hs.modrm >> 3) & 7;
        hs.modrm_rm = hs.modrm & 7;
        
        // Handle SIB byte
        if hs.modrm_mod != 3 && hs.modrm_rm == 4 {
            if p >= p_end {
                return 0;
            }
            
            hs.sib = *p;
            p = p.add(1);
            
            hs.sib_scale = (hs.sib >> 6) & 3;
            hs.sib_index = (hs.sib >> 3) & 7;
            hs.sib_base = hs.sib & 7;
            
            hs.flags |= Flags::FSib as u32;
        }
        
        // Handle displacement
        if hs.modrm_mod == 1 {
            // 8-bit displacement
            if p >= p_end {
                return 0;
            }
            
            hs.disp[0] = *p;
            p = p.add(1);
            
            hs.flags |= Flags::FDisp8 as u32;
        } else if hs.modrm_mod == 2 || (hs.modrm_mod == 0 && hs.modrm_rm == 5) {
            // 32-bit displacement
            if p.add(3) >= p_end {
                return 0;
            }
            
            hs.disp[0] = *p;
            hs.disp[1] = *p.add(1);
            hs.disp[2] = *p.add(2);
            hs.disp[3] = *p.add(3);
            p = p.add(4);
            
            hs.flags |= Flags::FDisp32 as u32;
        }
    }
    
    // Calculate instruction length
    hs.len = (p as usize - code as usize) as u8;
    
    hs.len
}
