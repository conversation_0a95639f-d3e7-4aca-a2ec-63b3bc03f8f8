fn main() {
    // <PERSON> Cargo to re-run this build script if any of the assembly files change
    println!("cargo:rerun-if-changed=src/asm/callr12.asm");
    println!("cargo:rerun-if-changed=src/asm/callme.asm");
    println!("cargo:rerun-if-changed=src/asm/spoof.asm");

    // Compile the assembly files
    cc::Build::new()
        .file("src/asm/callr12.asm")
        .file("src/asm/callme.asm")
        .file("src/asm/spoof.asm")
        .compile("koneko_asm");

    // Link the assembly files
    println!("cargo:rustc-link-lib=static=koneko_asm");
}
